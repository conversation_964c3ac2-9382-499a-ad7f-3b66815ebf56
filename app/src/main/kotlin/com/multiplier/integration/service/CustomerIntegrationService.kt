package com.multiplier.integration.service

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.multiplier.common.jobs.annotations.Job
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.ContractFilters
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.CountryServiceAdapter
import com.multiplier.integration.adapter.api.DefaultTriNetAPIAdapter
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.MemberServiceAdapter
import com.multiplier.integration.adapter.api.NewCompanyServiceAdapter
import com.multiplier.integration.adapter.model.knit.EmployeeData
import com.multiplier.integration.adapter.model.knit.EmployeeDetailData
import com.multiplier.integration.platforms.*
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.EventLogRepository
import com.multiplier.integration.repository.ManualSyncRepository
import com.multiplier.integration.repository.PendingEmployeeRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.PlatformEmployeeDataRepository
import com.multiplier.integration.repository.PlatformRepository
import com.multiplier.integration.repository.ProviderRepository
import com.multiplier.integration.repository.ReceivedEventRepository
import com.multiplier.integration.repository.SyncRepository
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.model.JpaManualSync
import com.multiplier.integration.repository.model.JpaPlatformContractIntegration
import com.multiplier.integration.repository.model.JpaReceivedEvent
import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.repository.type.ProviderName
import com.multiplier.integration.service.exception.BadRequestException
import com.multiplier.integration.service.exception.EntityNotFoundException
import com.multiplier.integration.service.exception.IntegrationIllegalArgumentException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.service.exception.IntegrationInternalServerException
import com.multiplier.integration.types.CompanyIntegrationInfo
import com.multiplier.integration.types.CustomerIntegration
import com.multiplier.integration.types.CustomerIntegrationDefinition
import com.multiplier.integration.types.DocumentReadable
import com.multiplier.integration.types.LatestSyncResult
import com.multiplier.integration.types.NotificationType
import com.multiplier.integration.types.PlatformCategory
import com.multiplier.integration.types.Position
import com.multiplier.integration.types.SyncEORManuallyResult
import com.multiplier.integration.types.SyncStatus
import com.multiplier.integration.types.SyncSummaryResultDownloadOutput
import com.multiplier.integration.types.SyncType
import com.multiplier.integration.types.TaskResponse
import com.multiplier.integration.utils.validateIntegrationCompanyMatch
import com.multiplier.pigeonservice.dto.Attachment
import io.ktor.util.*
import kotlinx.coroutines.runBlocking
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.PageRequest
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.util.*

@Service
class CustomerIntegrationService(
    private val companyIntegrationRepository: CompanyIntegrationRepository,
    private val syncRepository: SyncRepository,
    private val platformRepository: PlatformRepository,
    private val providerRepository: ProviderRepository,
    private val hibobHRPlatformStrategy: HibobHRPlatformStrategy,
    private val bambooHRPlatformStrategy: BambooHRPlatformStrategy,
    private val workdayHRPlatformStrategy: WorkdayHRPlatformStrategy,
    private val personioHRPlatformStrategy: PersonioHRPlatformStrategy,
    private val successfactorsHRPlatformStrategy: SuccessFactorsPlatformStrategy,
    private val platformContractIntegrationRepository: PlatformContractIntegrationRepository,
    private val platformEmployeeDataRepository: PlatformEmployeeDataRepository,
    private val knitAdapter: KnitAdapter,
    private val receivedEventRepository: ReceivedEventRepository,
    private val pendingEmployeeRepository: PendingEmployeeRepository,
    private val triNetPlatformStrategy: TriNetPlatformStrategy,
    private val contractServiceAdapter: ContractServiceAdapter,
    private val eventLogRepository: EventLogRepository,
    private val manualSyncRepository: ManualSyncRepository,
    private val triNetAPIAdapter: DefaultTriNetAPIAdapter,
    private val notificationsService: NotificationsService,
    private val memberService: MemberServiceAdapter,
    private val newCompanyServiceAdapter: NewCompanyServiceAdapter,
    private val integrationRepository: CompanyIntegrationRepository,
    private val countryServiceAdapter: CountryServiceAdapter,
    private val fieldMappingService: FieldMappingService,
    private val eventLogService: EventLogService,
    private val kekaHRPlatformStrategy: KekaHRPlatformStrategy,
    private val zohoPlatformStrategy: ZohoPlatformStrategy,
    private val adpWorkForceNowPlatformStrategy: ADPWorkForceNowPlatformStrategy,
    private val oracleHCMPlatformStrategy: OracleHCMPlatformStrategy,
    private val paychexPlatformStrategy: PaychexPlatformStrategy,
) {

    private val log = KotlinLogging.logger {}

    @Value("\${platform.base-url}")
    private lateinit var baseUrl: String

    private val objectMapper = jacksonObjectMapper()

    fun findCompanyConnectedIntegrationsByCompanyIds(
        companyIds: Set<Long>,
    ): Map<Long, List<CustomerIntegration>?> {
        if (companyIds.isEmpty()) return mutableMapOf()

        val connectedIntegrations =
            companyIntegrationRepository.findByCompanyIdIn(companyIds).groupBy { it.companyId }

        return connectedIntegrations.mapValues { (_, integrations) ->
            integrations.map {
                CustomerIntegration(
                    it.id,
                    it.platform.name,
                    it.platform.category,
                    it.enabled,
                    it.platform.isPositionDropdownEnabled
                )
            }
        }
    }

    fun getPlatformStrategyByCompanyId(companyId: Long): Pair<PlatformStrategy?, CustomerIntegration> {
        val integrationsForCompany = findCompanyConnectedIntegrationsByCompanyIds(setOf(companyId))
        val companyIntegrations = integrationsForCompany?.get(companyId) ?: emptyList()

        when {
            companyIntegrations.isEmpty() -> throw IntegrationIllegalStateException("No integration found for company id = $companyId")
            else -> {
                val activeIntegration = companyIntegrations.firstOrNull { it.category == PlatformCategory.HRIS && it.connected }
                    ?: throw IntegrationIllegalStateException("No active integration found for company id = $companyId")

                return Pair(determinePlatformStrategyByPlatformName(activeIntegration.name), activeIntegration)
            }
        }
    }

    fun getAccountingIntegrationInfo(companyId: Long): CustomerIntegration {
        val companyIntegrations = findCompanyConnectedIntegrationsByCompanyIds(setOf(companyId))[companyId] ?: emptyList()

        val activeIntegration = companyIntegrations.firstOrNull { it.category == PlatformCategory.ACCOUNTING && it.connected}
            ?: throw IntegrationIllegalStateException("No active integration found for company id = $companyId")

        return activeIntegration
    }

    private fun determinePlatformStrategyByPlatformName(platformName: String): PlatformStrategy? {
        return when (platformName.lowercase()) {
            "hibob" -> hibobHRPlatformStrategy
            "bamboohr" -> bambooHRPlatformStrategy
            "workday" -> workdayHRPlatformStrategy
            "personio" -> personioHRPlatformStrategy
            "successfactors" -> successfactorsHRPlatformStrategy
            "trinet" -> triNetPlatformStrategy
            "keka hr" -> kekaHRPlatformStrategy
            "zoho people" -> zohoPlatformStrategy
            "adp workforcenow"-> adpWorkForceNowPlatformStrategy
            "oracle hcm" -> oracleHCMPlatformStrategy
            "paychex" -> paychexPlatformStrategy
            else -> throw IntegrationIllegalArgumentException("Unsupported platform name: $platformName")
        }
    }

    @Transactional
    fun disconnectCompanyIntegration(
        integrationId: Long,
        currentUserCompanyId: Long?,
        currentUserExperience: String?,
        currentUserId: Long?,
    ): CustomerIntegration {
        val integration =
            companyIntegrationRepository.findById(integrationId).orElseThrow {
                EntityNotFoundException("Integration id=$integrationId not found")
            }

        if (!integration.enabled || integration.accountToken.isBlank()) {
            throw BadRequestException("Integration is either disabled or accountToken is empty")
        }

        if (currentUserExperience != "operations" &&
            currentUserCompanyId != integration.companyId
        ) {
            throw BadRequestException("Integration is not related to company id of current user")
        }

        val accountToken = integration.accountToken
        log.info("Resetting entry from company_integration table for integrationId=$accountToken")
        integration.accountToken = ""
        integration.enabled = false
        integration.outgoingSyncEnabled = false
        integration.incomingSyncEnabled = false
        integration.incomingSyncInProgress = false
        integration.importInProgress = false
        integration.lastIncomingSyncTime = null
        integration.lastOutgoingSyncTime = null
        integration.incomingContractorSyncEnabled = false
        integration.incomingContractorSyncInProgress = false
        integration.lastIncomingContractorSyncTime = null
        companyIntegrationRepository.save(integration)
        log.info("Deleting entry from received_events table for integrationId=$accountToken")
        receivedEventRepository.deleteByIntegrationId(accountToken)
        log.info("Deleting entry from sync table for integrationId=$accountToken")
        syncRepository.deleteByIntegrationId(accountToken)
        log.info("Deleting entry from pending_employee table for integrationId=$accountToken")
        pendingEmployeeRepository.deleteByIntegrationId(accountToken)

        log.info("disconnectCompanyIntegration successfully for integrationID=$integrationId")

        if (integration.platform.name == "TriNet") {
            notificationsService.sendAdminIntegrationDisconnected(
                integration.companyId,
                integration.platform.name,
                currentUserId!!,
                integration.platform.id!!
            )
        }

        fieldMappingService.handleFieldMappingsOnDisconnection(integrationId)

        return CustomerIntegration(
            integrationId,
            integration.platform.name,
            integration.platform.category,
            integration.enabled,
            integration.platform.isPositionDropdownEnabled
        )
    }

    fun listSupportedIntegrations(): List<CustomerIntegrationDefinition> {
        val supportedPlatform = platformRepository.findAll()

        return supportedPlatform.map {
            CustomerIntegrationDefinition(
                it.id,
                it.name,
                it.category,
                null,
                null,
                null,
            )
        }
    }

    fun getPositionsForIntegration(
        integrationId: Long,
        currentUserCompanyId: Long?,
        isOpsUser: Boolean = false,
    ): List<Position> {
        val integration = companyIntegrationRepository.findById(integrationId)
            .orElseThrow { IntegrationIllegalStateException("Integration not found for integrationId=$integrationId") }

        validateIntegrationCompanyMatch(currentUserCompanyId, integration.companyId, isOpsUser)

        val providerId = integration.provider.id
            ?: throw IntegrationIllegalStateException("Provider ID not found for integrationId=$integrationId")

        val provider = providerRepository.findById(providerId)
            .orElseThrow { IntegrationIllegalStateException("Provider not found for providerId=$providerId") }

        if (provider.name != ProviderName.KNIT) {
            throw IntegrationIllegalStateException("Unsupported provider: ${provider.name}")
        }

        val platformId = integration.platform.id
            ?: throw IntegrationIllegalStateException("Platform ID not found for integrationId=$integrationId")

        val positionsData = runBlocking {
            knitAdapter.getPositionsDetails(integration.companyId, platformId)
        } ?: throw IntegrationIllegalStateException("Failed to retrieve positions data")

        if (!positionsData.success) {
            throw IntegrationInternalServerException("API call to get positions was unsuccessful: ${positionsData.error?.msg}")
        }

        val positions = mutableListOf<Position>()

        val titleRestrictions = countryServiceAdapter.getContractDetailRestriction()
            .filter { it.countryCode == com.multiplier.country.schema.Country.GrpcCountryCode.NULL_COUNTRY_CODE }
            .associate { it.searchTerm.toLowerCasePreservingASCIIRules() to it.alternativesList }

        positionsData.data?.positions?.forEach { data ->
            val positionId = data.positionId ?: ""
            val designation = data.designation ?: ""
            val department = data.department ?: ""

            if (titleRestrictions.containsKey(designation.toLowerCasePreservingASCIIRules())) {
                titleRestrictions[designation.toLowerCasePreservingASCIIRules()]?.forEach {
                    positions.add(Position(positionId, it, department))
                }
            } else
                positions.add(Position(positionId, designation, department))
        }

        return positions
    }

    fun dismissSyncResult(syncId: String, currentUserCompanyId: Long?, isOpsUser: Boolean = false): TaskResponse {
        log.info("Received request to dismiss sync result for syncId: $syncId")

        val manualSync = manualSyncRepository.findBySyncId(syncId).orElse(null)
        if (manualSync == null) {
            val errorMessage = "Manual sync not found for syncId=$syncId"
            log.error(errorMessage)
            return TaskResponse(false, errorMessage)
        }

        val integration = companyIntegrationRepository.findById(manualSync.integrationId).orElse(null)
        if (integration == null) {
            val errorMessage = "Integration not found for integrationId=${manualSync.integrationId}"
            log.error(errorMessage)
            return TaskResponse(false, errorMessage)
        }

        try {
            validateIntegrationCompanyMatch(currentUserCompanyId, integration.companyId, isOpsUser)
        } catch (e: Exception) {
            log.error("Validation error: ${e.message}")
            return TaskResponse(false, e.message ?: "Unknown validation error")
        }

        // Dismiss the sync result
        manualSync.dismissedOn = LocalDateTime.now()
        manualSyncRepository.save(manualSync)

        log.info("Sync result dismissed successfully for syncId: $syncId")
        return TaskResponse(true, "Sync result dismissed successfully")
    }

    fun syncEORManually(
        integrationId: Long,
        currentUserCompanyId: Long?,
        isOpsUser: Boolean = false,
    ): SyncEORManuallyResult {
        log.info("Received request to sync EOR manually for integrationId: $integrationId")

        val integration = companyIntegrationRepository.findById(integrationId)
            .orElseThrow { IntegrationIllegalStateException("Integration not found for integrationId=$integrationId") }

        validateIntegrationCompanyMatch(currentUserCompanyId, integration.companyId, isOpsUser)

        val latestSyncResult = getLatestSyncResultForIntegration(integrationId, currentUserCompanyId, false, isOpsUser)
        if (latestSyncResult != null && latestSyncResult.status == SyncStatus.IN_PROGRESS) {
            log.info("Manual sync is already in progress for integrationId: $integrationId")
            return SyncEORManuallyResult(null, integrationId, false, "Manual sync is already in progress")
        }

        val syncId = UUID.randomUUID().toString()
        handleOutgoingSyncEnabled(integration, syncId)

        val manualSync = JpaManualSync(
            syncId = syncId,
            integrationId = integrationId,
            status = SyncStatus.IN_PROGRESS,
            type = SyncType.MANUAL_OUTGOING,
            startedOn = LocalDateTime.now(),
            completedOn = null,
            dismissedOn = null
        )

        manualSyncRepository.save(manualSync)

        return SyncEORManuallyResult(manualSync.syncId, integrationId, true, "Manual sync started successfully")
    }

    fun validateIntegrationCredentials(
        integrationId: Long,
        currentUserCompanyId: Long?,
        validateUser: Boolean,
        isOpsUser: Boolean = false,
    ): TaskResponse {
        log.info("Validating integration credentials for Integration ID: {}", integrationId)

        val integration = companyIntegrationRepository.findById(integrationId)
            .orElseThrow { IntegrationIllegalStateException("Integration not found for Integration ID: $integrationId") }
        log.info("Integration retrieved: {}", integration)

        if (validateUser) {
            validateIntegrationCompanyMatch(currentUserCompanyId, integration.companyId, isOpsUser)
        }
        log.info("Company ID match validated for Integration ID: {}", integrationId)

        if (!integration.enabled) {
            log.warn("Attempt to validate credentials for a disabled integration. Integration ID: {}", integrationId)
            throw IntegrationIllegalStateException("Integration is not enabled for Integration ID: $integrationId")
        }

        if (integration.provider.name != ProviderName.TRINET) {
            log.error("Unsupported provider: {} for Integration ID: {}", integration.provider.name, integrationId)
            throw IntegrationIllegalStateException("Unsupported provider: ${integration.provider.name}")
        }

        val accessToken = triNetAPIAdapter.getAccessToken(integration.accountName!!, integration.accountToken)
        if (accessToken == null) {
            log.warn("Access token retrieval failed for Integration ID: {}", integrationId)
            handleIntegrationCredentialFailed(integrationId)
            return TaskResponse(false, "Invalid or expired credentials")
        }

        log.info("Integration credentials validated successfully for Integration ID: {}", integrationId)
        return TaskResponse(true, "Integration credentials validated successfully")
    }

    @Async
    fun handleIntegrationCredentialFailed(integrationId: Long) {
        log.info("Handling failed integration credentials for Integration ID: {}", integrationId)

        val integration = companyIntegrationRepository.findById(integrationId)
            .orElseThrow { IntegrationIllegalStateException("Integration not found for Integration ID: $integrationId") }

        integration.apply {
            enabled = false
            outgoingSyncEnabled = false
        }
        companyIntegrationRepository.save(integration)
        log.info("Integration disabled and saved for Integration ID: {}", integrationId)

        notificationsService.sendAdminIntegrationCredentialExpired(integration.companyId, integration.platform.name)
        log.info("Admin notification sent for expired credentials. Integration ID: {}", integrationId)
    }

    @Async
    fun getLatestSyncResultForIntegrationInternal(integrationId: Long) {
        log.info("Received internal request to get the latest sync result for integrationId: $integrationId")
        getLatestSyncResultForIntegration(integrationId, null, true)
    }

    fun getLatestSyncResultForIntegration(
        integrationId: Long,
        currentUserCompanyId: Long?,
        internalUse: Boolean,
        isOpsUser: Boolean = false,
    ): LatestSyncResult? {

        log.info("Received request to get the latest sync result for integrationId: $integrationId")

        val integration = companyIntegrationRepository.findById(integrationId)
            .orElseThrow { IntegrationIllegalStateException("Integration not found for integrationId=$integrationId") }

        if (!internalUse) {
            validateIntegrationCompanyMatch(currentUserCompanyId, integration.companyId, isOpsUser)
        }

        val latestManualSync = findLatestManualSync(integrationId) ?: return null
        val events = eventLogRepository.findBySyncId(latestManualSync.syncId)
        log.info("Found ${events.size} events for syncId: ${latestManualSync.syncId}")

        var contractsAnalysis = analyzeEventsByContract(events)

        if (latestManualSync.status != SyncStatus.IN_PROGRESS) {
            return constructLatestSyncResult(latestManualSync, contractsAnalysis)
        }

        if (contractsAnalysis.allProcessed) {
            log.info("All events have been processed for latestManualSync Id: ${latestManualSync.syncId}")
            if (contractsAnalysis.failedContracts == 0) {
                log.info("All events have been processed successfully for latestManualSync Id: ${latestManualSync.syncId}")
                latestManualSync.status = SyncStatus.SUCCESS
            } else {
                // FAILED here means that at least one event failed. In Future if required, introduce status PARTIAL_SUCCESS
                log.info("Some events have failed for latestManualSync Id: ${latestManualSync.syncId}")
                latestManualSync.status = SyncStatus.FAILED
            }
            val shouldExportSyncResult = shouldExportSyncSummaryResult(latestManualSync, events)
            if (latestManualSync.completedOn == null) {
                val completedOn =
                    if (events.isEmpty()) LocalDateTime.now() else events.maxByOrNull { it.updatedOn!! }?.updatedOn
                latestManualSync.completedOn = completedOn
                latestManualSync.dismissedOn = latestManualSync.completedOn?.plusHours(24)
            }
            manualSyncRepository.save(latestManualSync)
            if (shouldExportSyncResult) {
                val manualSyncs = manualSyncRepository.findByIntegrationIdOrderByCreatedOnDesc(integrationId)!!
                if (manualSyncs.size >= 2) {
                    val previousSync = manualSyncs[1]

                    // CU-86cvf744q : include all others auto sync events prior to the current manual sync as well
                    val summaryEvents = eventLogRepository.findByCompanyIdAndEventTimeRange(
                        integration.companyId,
                        previousSync.startedOn,
                        latestManualSync.startedOn,
                        listOf(
                            EventType.INCOMING_MEMBER_BASIC_DETAILS_UPDATED,
                            EventType.INCOMING_MEMBER_ADDRESS_UPDATED,
                            EventType.INCOMING_MEMBER_LEGAL_DATA_UPDATED,
                            EventType.INCOMING_OFFBOARDING_STATUS_UPDATE,
                            EventType.INCOMING_SALARY_REVIEW_ACTIVATED,
                            EventType.INCOMING_CONTRACT_DOCUMENT_STATUS_UPDATE,
                            EventType.INCOMING_PAYROLL_PAYSLIP_UPLOADED,
                            EventType.INCOMING_PAYROLL_PAYSLIP_PUBLISHED,
                            EventType.INCOMING_CONTRACT_WORK_EMAIL_CHANGED,
                        )
                    )

                    val allEvents = mutableListOf<JpaEventLog>()
                    allEvents.addAll(events)
                    if (summaryEvents != null) {
                        allEvents.addAll(summaryEvents)
                    }
                    // update contract analysis to contain autosync events
                    contractsAnalysis = analyzeEventsByContract(allEvents)
                    exportSyncSummaryResult(allEvents, contractsAnalysis, integration)
                } else {
                    exportSyncSummaryResult(events, contractsAnalysis, integration)
                }
            }
        } else {
            log.info("Not all events have been processed for latestManualSync Id: ${latestManualSync.syncId}")
        }

        return constructLatestSyncResult(latestManualSync, contractsAnalysis)
    }

    private fun shouldExportSyncSummaryResult(manualSync: JpaManualSync, events: List<JpaEventLog>): Boolean {
        return manualSync.completedOn == null && events.isNotEmpty()
    }

    private fun fetchEmployeeDetails(contractId: Long): EmployeeDetailData {
        val contract = contractServiceAdapter.findContractByContractId(contractId)
        val member = memberService.findMemberByMemberId(contract.memberId)
        val firstMemberAddress = if (member.addressesCount > 0) member.getAddresses(0) else null
        val primaryEmail = member.emailsList
            .firstOrNull { it.type == "primary" }
        val contactNumber = if (member.phoneNosCount > 0) member.getPhoneNos(0).phoneNo else null

        return EmployeeDetailData(
            firstName = member.firstName,
            lastName = member.lastName,
            addressLine1 = firstMemberAddress?.line1,
            addressLine2 = firstMemberAddress?.line2,
            country = firstMemberAddress?.country?.name,
            postalCode = firstMemberAddress?.postalCode,
            city = firstMemberAddress?.city,
            emailAddress = primaryEmail?.email,
            designation = contract.position,
            contactNumber = contactNumber,
        )
    }

    private fun generateTriNetSyncSummaryResult(
        events: List<JpaEventLog>,
        integration: JpaCompanyIntegration,
    ): ByteArray? {
        var results = mutableListOf<SyncSummaryResult>()
        val createdEventTypes = listOf(
            EventType.SERVICE_INTERNAL_CREATE_CONTRACT,
            EventType.INCOMING_ONBOARDING_STATUS_UPDATE,
        )
        val updatedEventTypes = listOf(
            EventType.INCOMING_MEMBER_BASIC_DETAILS_UPDATED,
            EventType.INCOMING_MEMBER_ADDRESS_UPDATED,
            EventType.INCOMING_MEMBER_LEGAL_DATA_UPDATED,
            EventType.INCOMING_CONTRACT_WORK_EMAIL_CHANGED
        )
        val eventTypesToInclude = createdEventTypes + updatedEventTypes
        //FIXME need to handle this error before saving to event logs
        val UNSUPPORTED_EVENT_ERROR = "HR_MEMBER contract type is not supported for TriNet platform."
        val UNSUPPORT_EXISTED_EVENT_ERROR = "EXISTED"
        val UNSUPPORTED_OUTGOING_SYNC_DISABLE = "Outgoing sync is disabled for integration id"
        val filteredEvents = events.filter {
            it.eventType in eventTypesToInclude && it.errorMessage?.contains(UNSUPPORTED_EVENT_ERROR) != true &&
                    it.errorMessage?.contains(UNSUPPORT_EXISTED_EVENT_ERROR) != true &&
                    it.errorMessage?.contains(UNSUPPORTED_OUTGOING_SYNC_DISABLE) != true
        }
        val groupEventsByContractId = filteredEvents.groupBy { it.contractId }
        for ((contractId, eventList) in groupEventsByContractId) {
            if (contractId == null) {
                log.warn("generateTriNetSyncSummaryResult contractId null")
                continue
            }
            if (hasAnyFailedEvent(eventList)) {
                val errorMessages =
                    eventList.filter { it.status == EventStatus.FAILED || it.status == EventStatus.IGNORED }
                        .map { if (it.errorMessage.isNullOrEmpty()) "Could not be synced due to an unknown error" else it.errorMessage }
                        .distinct()
                val triNetData = fetchEmployeeDetails(contractId)
                results.add(
                    SyncSummaryResult(
                        status = EventStatus.FAILED.name,
                        contractId = contractId,
                        eventType = null,
                        error = errorMessages.joinToString("\n"),
                        employeeInfo = EmployeeInfo(
                            firstName = EmployeeDetail(
                                value = triNetData?.firstName
                            ),
                            lastName = EmployeeDetail(
                                value = triNetData?.lastName
                            ),
                            emailAddress = EmployeeDetail(
                                value = triNetData?.emailAddress
                            ),
                            designation = EmployeeDetail(
                                value = triNetData?.designation
                            ),
                            startDate = EmployeeDetail(
                                value = triNetData?.startDate
                            ),
                            locationId = EmployeeDetail(
                                value = triNetData?.locationId
                            ),
                            addressLine1 = EmployeeDetail(
                                value = triNetData?.addressLine1
                            ),
                            addressLine2 = EmployeeDetail(
                                value = triNetData?.addressLine2
                            ),
                            province = EmployeeDetail(
                                value = triNetData?.city
                            ),
                            country = EmployeeDetail(
                                value = triNetData?.country
                            ),
                            postalCode = EmployeeDetail(
                                value = triNetData?.postalCode
                            ),
                            workEmailAddress = EmployeeDetail(
                                value = triNetData?.workEmailAddress
                            ),
                            contactNumber = EmployeeDetail(
                                value = triNetData?.contactNumber
                            )
                        )
                    )
                )
            }
            if (!hasAnySuccessEvent(eventList)) {
                continue
            }
            val successEvents = eventList.filter { it.status == EventStatus.SUCCESS }

            val employeeContractIntegration = platformContractIntegrationRepository
                .findFirstByContractIdAndProviderIdAndPlatformId(
                    contractId,
                    integration.provider.id,
                    integration.platform.id
                )
                ?: continue
            val platformEmployeeData =
                platformEmployeeDataRepository.findByEmployeeId(employeeContractIntegration.platformEmployeeId)
                    .firstOrNull() ?: continue
            val employeeData = objectMapper.readValue(platformEmployeeData.employeeData, EmployeeData::class.java)
            val triNetData = employeeData.employeeDetailData

            val groupedEvents = mapOf(
                EventType.SERVICE_INTERNAL_CREATE_CONTRACT to successEvents.filter { it.eventType in createdEventTypes },
                EventType.INCOMING_MEMBER_BASIC_DETAILS_UPDATED to successEvents.filter { it.eventType in updatedEventTypes }
            )
            for ((eventType, groupEventList) in groupedEvents) {
                if (groupEventList.isEmpty()) {
                    continue
                }
                results.add(
                    SyncSummaryResult(
                        status = EventStatus.SUCCESS.name,
                        contractId = contractId,
                        eventType = eventType,
                        employeeInfo = EmployeeInfo(
                            firstName = EmployeeDetail(
                                value = triNetData?.firstName
                            ),
                            lastName = EmployeeDetail(
                                value = triNetData?.lastName
                            ),
                            emailAddress = EmployeeDetail(
                                value = triNetData?.emailAddress
                            ),
                            designation = EmployeeDetail(
                                value = triNetData?.designation
                            ),
                            startDate = EmployeeDetail(
                                value = triNetData?.startDate
                            ),
                            locationId = EmployeeDetail(
                                value = triNetData?.locationId
                            ),
                            addressLine1 = EmployeeDetail(
                                value = triNetData?.addressLine1
                            ),
                            addressLine2 = EmployeeDetail(
                                value = triNetData?.addressLine2
                            ),
                            province = EmployeeDetail(
                                value = triNetData?.city
                            ),
                            country = EmployeeDetail(
                                value = triNetData?.country
                            ),
                            postalCode = EmployeeDetail(
                                value = triNetData?.postalCode
                            ),
                            workEmailAddress = EmployeeDetail(
                                value = triNetData?.workEmailAddress
                            ),
                            contactNumber = EmployeeDetail(
                                value = triNetData?.contactNumber
                            )
                        ),
                        error = null
                    )
                )
            }
        }

        if (results.isNotEmpty()) {
            val input = getResourceAsStream("/templates/sync-summary-file.xlsx")
            return ExcelResultGenerator.addSyncSummaryResultToInputSheet(
                inputSheet = input,
                results = results,
            )
        }
        return null
    }

    private fun triggerSendingResultEmailToAdmins(
        companyId: Long,
        resultFileByteArray: ByteArray,
        contractsAnalysis: ContractsAnalysis,
    ) {
        val companyAdmins = newCompanyServiceAdapter.getCompanyAdmins(companyId)
        val templateParams =
            mutableMapOf(
                "addedEmployees" to contractsAnalysis.addedContracts.toString(),
                "updatedEmployees" to contractsAnalysis.updatedContracts.toString(),
                "failedEmployees" to contractsAnalysis.failedContracts.toString(),
                "link" to baseUrl
            )
        val attachments = listOf(
            Attachment(
                "sync-summary-file.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                resultFileByteArray
            )
        )
        notificationsService.sendingResultEmail(
            companyAdmins,
            templateType = NotificationType.IntegrationSyncResultsEmailToAdmin,
            templateParams = templateParams,
            attachments = attachments,
            subject = "Review employee data sync results with Trinet."
        )
    }

    @Async
    fun exportSyncSummaryResult(
        events: List<JpaEventLog>,
        contractsAnalysis: ContractsAnalysis,
        integration: JpaCompanyIntegration,
    ) {
        try {
            require(integration.platform.name == "TriNet") { "Other platforms are not implemented" }
            val resultFileByteArray = generateTriNetSyncSummaryResult(events, integration) ?: throw IntegrationIllegalStateException("Empty sync result for company id ${integration.companyId}")
            triggerSendingResultEmailToAdmins(integration.companyId, resultFileByteArray, contractsAnalysis)
        } catch (e: Exception) {
            log.error("exportSyncSummaryResult exception: ", e)
        }
    }

    private fun findLatestManualSync(integrationId: Long): JpaManualSync? {
        return manualSyncRepository.findTopByIntegrationIdOrderByCreatedOnDesc(integrationId).orElse(null)?.also {
            log.info("Latest manual sync found for integrationId: $integrationId")
        } ?: run {
            log.warn("No manual sync found for integrationId: $integrationId")
            null
        }
    }

    private fun analyzeEventsByContract(events: List<JpaEventLog>): ContractsAnalysis {
        var addedContracts = 0
        var updatedContracts = 0
        var failedContracts = 0

        val hasEventsToBeProcessedOrProcessing = events.any {
            it.status == EventStatus.TO_BE_PROCESSED || it.status == EventStatus.PROCESSING || it.status == EventStatus.TO_BE_PROCESSED_AGAIN
        }
        if (hasEventsToBeProcessedOrProcessing) {
            log.info("Detected events with status TO_BE_PROCESSED or PROCESSING, aborting analysis.")
            return ContractsAnalysis(0, 0, 0, false)
        }

        //FIXME need to handle this error before saving to event logs
        val UNSUPPORTED_EVENT_ERROR = "HR_MEMBER contract type is not supported for TriNet platform."
        val UNSUPPORT_EXISTED_EVENT_ERROR = "EXISTED"
        val UNSUPPORTED_OUTGOING_SYNC_DISABLE = "Outgoing sync is disabled for integration id"

        val createdEventTypes = listOf(
            EventType.SERVICE_INTERNAL_CREATE_CONTRACT,
            EventType.INCOMING_ONBOARDING_STATUS_UPDATE,
        )
        val updatedEventTypes = listOf(
            EventType.INCOMING_MEMBER_BASIC_DETAILS_UPDATED,
            EventType.INCOMING_MEMBER_ADDRESS_UPDATED,
            EventType.INCOMING_MEMBER_LEGAL_DATA_UPDATED,
            EventType.INCOMING_CONTRACT_WORK_EMAIL_CHANGED
        )
        val eventTypesToInclude = createdEventTypes + updatedEventTypes
        val filteredEvents = events.filter {
            it.eventType in eventTypesToInclude && it.errorMessage?.contains(UNSUPPORTED_EVENT_ERROR) != true &&
                    it.errorMessage?.contains(UNSUPPORT_EXISTED_EVENT_ERROR) != true &&
                    it.errorMessage?.contains(UNSUPPORTED_OUTGOING_SYNC_DISABLE) != true
        }

        filteredEvents.groupBy { it.contractId }.forEach { (_, eventList) ->
            when {
                hasSuccessfulCreateContractEvent(eventList, createdEventTypes) -> addedContracts++
                hasAnyFailedEvent(eventList) -> failedContracts++
                else -> updatedContracts++
            }
        }

        return ContractsAnalysis(addedContracts, updatedContracts, failedContracts, true)
    }

    private fun hasSuccessfulCreateContractEvent(
        events: List<JpaEventLog>,
        createdEventTypes: List<EventType>,
    ): Boolean =
        events.any { it.eventType in createdEventTypes && it.status == EventStatus.SUCCESS }

    private fun hasCreateContractEvent(events: List<JpaEventLog>): Boolean =
        events.any { it.eventType == EventType.SERVICE_INTERNAL_CREATE_CONTRACT }

    private fun hasAnyFailedEvent(events: List<JpaEventLog>): Boolean =
        events.any { it.status == EventStatus.FAILED || it.status == EventStatus.IGNORED }

    private fun hasAnySuccessEvent(events: List<JpaEventLog>): Boolean =
        events.any { it.status == EventStatus.SUCCESS }

    private fun constructLatestSyncResult(
        latestManualSync: JpaManualSync,
        analysis: ContractsAnalysis,
    ): LatestSyncResult {
        return LatestSyncResult.newBuilder()
            .syncId(latestManualSync.syncId)
            .integrationId(latestManualSync.integrationId)
            .status(latestManualSync.status)
            .addedContracts(analysis.addedContracts)
            .updatedContracts(analysis.updatedContracts)
            .failedContracts(analysis.failedContracts)
            .completedAt(latestManualSync.completedOn)
            .createdAt(latestManualSync.createdOn)
            .updatedAt(latestManualSync.updatedOn)
            .dismissedAt(latestManualSync.dismissedOn)
            .build()
    }

    fun startTimeoffSync(
        integrationId: Long,
        currentUserCompanyId: Long?,
        category: PlatformCategory,
        isOpsUser: Boolean = false,
    ) {
        var integration = integrationRepository.findById(integrationId).get()
        validateIntegrationCompanyMatch(currentUserCompanyId, integration.companyId, isOpsUser)
        var accountToken = integration.accountToken
        integration.incomingSyncInProgress = true
        integrationRepository.save(integration)
        knitAdapter.startSync(accountToken, category) ?: throw RuntimeException("Could not initiate sync")
    }

    @Transactional
    fun changeSyncState(
        integrationId: Long,
        syncType: SyncType,
        isEnabled: Boolean,
        currentUserCompanyId: Long?,
        isOpsUser: Boolean = false,
    ): TaskResponse {
        log.info(
            "Attempting to change sync state. Integration ID: {}, Sync Type: {}, State: {}, currentUserCompanyId: {}",
            integrationId, syncType, isEnabled, currentUserCompanyId
        )
        return try {
            val integration = fetchIntegration(integrationId)
            validateIntegrationCompanyMatch(currentUserCompanyId, integration.companyId, isOpsUser)
            updateSyncState(integration, syncType, isEnabled)
            companyIntegrationRepository.save(integration)

            if (isEnabled && syncType == SyncType.TIMEOFF && !integration.incomingSyncEnabled) {
                // start initial sync if GP sync is not enabled
                runBlocking {
                    startTimeoffSync(integrationId, currentUserCompanyId, PlatformCategory.HRIS)
                }
            }

            if (isEnabled && syncType == SyncType.OUTGOING) {
                val syncId = UUID.randomUUID().toString()
                handleOutgoingSyncEnabled(integration, syncId)
                val manualSync = JpaManualSync(
                    syncId = syncId,
                    integrationId = integrationId,
                    status = SyncStatus.IN_PROGRESS,
                    type = SyncType.OUTGOING,
                    startedOn = LocalDateTime.now(),
                    completedOn = null,
                    dismissedOn = null
                )

                manualSyncRepository.save(manualSync)
            } else if (!isEnabled && syncType == SyncType.OUTGOING) {
                notificationsService.sendAdminOutgoingSyncDisabled(
                    integration.companyId,
                    integration.platform.name,
                    integration.platform.id
                )
            }

            log.info("Sync state updated successfully for integration ID: {}", integrationId)
            TaskResponse(true, "Sync state updated successfully")
        } catch (e: Exception) {
            log.error("Error updating sync state for integration ID: {}. Error: {}", integrationId, e.message, e)
            TaskResponse(false, "Error updating sync state: ${e.message}")
        }
    }

    @Async
    fun handleOutgoingSyncEnabled(integration: JpaCompanyIntegration, syncId: String) {
        log.info("Handling Outgoing sync enabled for integrationId: ${integration.id}")
        val company = newCompanyServiceAdapter.getCompanyById(integration.companyId)

        val contractFilters = ContractFilters.newBuilder()
            .addCompanyIds(integration.companyId)
            .addContractStatuses(ContractOuterClass.ContractStatus.ACTIVE)
            .addContractStatuses(ContractOuterClass.ContractStatus.OFFBOARDING)
            .build()

        val testContractFilters = ContractFilters.newBuilder()
            .addCompanyIds(integration.companyId)
            .addContractStatuses(ContractOuterClass.ContractStatus.ACTIVE)
            .addContractStatuses(ContractOuterClass.ContractStatus.OFFBOARDING)
            .setIsTestContract(true)
            .build()

        val contractIds = contractServiceAdapter.getContractIdsByContractFilters(contractFilters, company.isTest)
        val testContractIds =
            contractServiceAdapter.getContractIdsByContractFilters(testContractFilters, company.isTest)

        // Prepare a list to hold all events before saving
        val eventLogs = ArrayList<JpaEventLog>()

        log.info { "Existed contract with status ACTIVE and OFFBOARDING: ${(contractIds + testContractIds).distinct()}" }

        (contractIds + testContractIds).distinct().forEach { contractId ->
            processExistingContractForEORSync(integration, contractId, syncId, eventLogs)
        }

        // Bulk save all at once
        eventLogRepository.saveAll(eventLogs)
    }

    private fun processExistingContractForEORSync(
        integration: JpaCompanyIntegration,
        contractId: Long,
        syncId: String,
        eventLogs: MutableList<JpaEventLog>,
    ) {
        try {
            // Skip processing for externally created contracts.
            if (checkIfContractIsExternallyCreated(integration, contractId)) {
                log.info("Ignoring externally created contract with ID: $contractId.")
                return
            }

            var isCreated = true
            // If the contract hasn't been processed, create a new internal event.
            if (!checkIfContractIsProcessedByIntegration(integration, contractId)) {
                log.info("Creating internal event for new employee creation for contract ID: $contractId.")
                eventLogs.add(createEventForContract(contractId, syncId, EventType.SERVICE_INTERNAL_CREATE_CONTRACT))
                isCreated = false
            }

            // For contracts already processed by the integration, handle updates.
            log.info("Processing updates for contract ID: $contractId already processed by integration.")
            val events = fetchRelevantEventsForContractUpdate(integration, contractId)

            val nonNullEvents = events.filterNotNull()

            if (nonNullEvents.isEmpty()) {
                log.info("No relevant events found for updating contract with ID: $contractId.")
                return
            }

            // Group events by type and find the latest event for each type.
            val latestEventPerType = nonNullEvents.groupBy { it.eventType }
                .mapValues { (_, v) -> v.maxByOrNull { it.createdOn!! } }

            // Mark all events as IGNORED and duplicate the latest event of each type.
            nonNullEvents.forEach { event ->
                event.status = EventStatus.FAILED
                eventLogRepository.save(event)

                if (isCreated) {
                    if (latestEventPerType[event.eventType] == event) {
                        val duplicateEvent = createEventForUpdate(event, syncId)
                        eventLogs.add(duplicateEvent)
                    }
                }
            }
        } catch (e: Exception) {
            log.error("Error processing contract with ID: $contractId", e)
        }
    }

    private fun fetchRelevantEventsForContractUpdate(
        integration: JpaCompanyIntegration,
        contractId: Long,
    ): List<JpaEventLog?> {
        // In case of this error, legacy integrations data can be an issue. Why?
        // Because we are trying to update an internally created contract which has been processed by the integration
        // and yet lastOutgoingSyncTimeToggleOffTime is not set.
        if (integration.lastOutgoingSyncTimeToggleOffTime == null) {
            throw IntegrationInternalServerException("Request to update contract without lastOutgoingSyncTimeToggleOffTime set.")
        }
        return eventLogRepository.findByContractIdAndEventTimeRange(
            contractId,
            integration.lastOutgoingSyncTimeToggleOffTime,
            LocalDateTime.now(),
            listOf(
                EventType.INCOMING_MEMBER_BASIC_DETAILS_UPDATED,
                EventType.INCOMING_MEMBER_ADDRESS_UPDATED,
                EventType.INCOMING_MEMBER_LEGAL_DATA_UPDATED,
                EventType.INCOMING_OFFBOARDING_STATUS_UPDATE,
                EventType.INCOMING_SALARY_REVIEW_ACTIVATED,
                EventType.INCOMING_CONTRACT_DOCUMENT_STATUS_UPDATE,
                EventType.INCOMING_PAYROLL_PAYSLIP_UPLOADED,
                EventType.INCOMING_PAYROLL_PAYSLIP_PUBLISHED,
                EventType.INCOMING_CONTRACT_WORK_EMAIL_CHANGED,
            ),
            EventStatus.IGNORED
        ) ?: emptyList()
    }

    private fun checkIfContractIsProcessedByIntegration(integration: JpaCompanyIntegration, contractId: Long): Boolean {
        log.info("Checking if contract with ID: $contractId has been processed by integration")

        val employeeContractIntegration =
            platformContractIntegrationRepository.findFirstByContractIdAndProviderIdAndPlatformId(
                contractId,
                integration.provider.id,
                integration.platform.id
            )

        return if (employeeContractIntegration != null) {
            log.info("Contract with ID: $contractId has been processed by integration")
            true
        } else {
            log.info("Contract with ID: $contractId has not been processed by integration")
            false
        }
    }

    private fun checkIfContractIsExternallyCreated(integration: JpaCompanyIntegration, contractId: Long): Boolean {
        log.info("Checking if contract is externally created for contractId: $contractId")

        val employeeContractIntegration =
            platformContractIntegrationRepository.findFirstByContractIdAndProviderIdAndPlatformId(
                contractId,
                integration.provider.id,
                integration.platform.id
            )
        if (employeeContractIntegration == null) {
            log.info("No contract integration found for contractId: $contractId")
            return false
        }

        val employeeData =
            platformEmployeeDataRepository.findByEmployeeId(employeeContractIntegration.platformEmployeeId)
                .firstOrNull()
        if (employeeData == null) {
            log.info("No employee data found for platformEmployeeId: ${employeeContractIntegration.platformEmployeeId}")
            return false
        }

        val isExternallyCreated = employeeData.origin == "EXTERNAL"
        if (isExternallyCreated) {
            log.info("Contract with contractId: $contractId is externally created.")
        } else {
            log.info("Contract with contractId: $contractId is not externally created.")
        }

        return isExternallyCreated
    }

    private fun createEventForContract(contractId: Long, syncId: String, eventType: EventType): JpaEventLog =
        JpaEventLog(
            eventType = eventType,
            eventId = UUID.randomUUID().toString(),
            eventPayload = "",
            status = EventStatus.TO_BE_PROCESSED,
            retriesLeft = 3,
            retriesDone = 0,
            nextAttempt = LocalDateTime.now(),
            errorMessage = null,
            contractId = contractId,
            syncId = syncId
        )

    private fun createEventForUpdate(event: JpaEventLog, syncId: String): JpaEventLog =
        JpaEventLog(
            eventType = event.eventType,
            eventId = UUID.randomUUID().toString(),
            eventPayload = event.eventPayload,
            status = EventStatus.TO_BE_PROCESSED,
            retriesLeft = 3,
            retriesDone = 0,
            nextAttempt = LocalDateTime.now(),
            errorMessage = null,
            contractId = event.contractId,
            syncId = syncId
        )

    private fun fetchIntegration(integrationId: Long): JpaCompanyIntegration {
        val integration = companyIntegrationRepository.findById(integrationId)
            .orElseThrow { Exception("Integration not found") }
        log.info("Fetched integration for updating: {}", integration)
        return integration
    }

    private fun updateSyncState(integration: JpaCompanyIntegration, syncType: SyncType, isEnabled: Boolean) {
        if (!integration.enabled && isEnabled) {
            throw IntegrationIllegalStateException("Cannot enable sync state as the integration is not enabled")
        }

        when (syncType) {
            SyncType.INCOMING -> {
                integration.incomingSyncEnabled = isEnabled
                log.info("Set incoming sync enabled to {}", isEnabled)
            }

            SyncType.OUTGOING -> {
                integration.outgoingSyncEnabled = isEnabled
                if (isEnabled) {
                    integration.lastOutgoingSyncTimeToggleOnTime = LocalDateTime.now()
                } else {
                    integration.lastOutgoingSyncTimeToggleOffTime = LocalDateTime.now()
                }
                log.info("Set outgoing sync enabled to {}", isEnabled)
            }

            SyncType.TIMEOFF -> {
                integration.timeOffSyncEnabled = isEnabled
                log.info("Set incoming sync enabled to {}", isEnabled)
            }

            SyncType.MANUAL_OUTGOING -> {
                throw IntegrationInternalServerException("Manual outgoing sync is not supported here")
            }

            SyncType.INCOMING_CONTRACTOR -> {
                integration.incomingContractorSyncEnabled = isEnabled
                log.info("Set incoming contractor sync enabled to {}", isEnabled)
            }
        }
    }

    fun findPlatformContractIntegrationFromEvent(
        event: JpaReceivedEvent,
        platformEmployeeId: String,
    ): Pair<JpaCompanyIntegration, JpaPlatformContractIntegration?> {
        val integrationId = syncRepository.findBySyncId(event.syncId!!).get()?.integrationId
            ?: throw jakarta.persistence.EntityNotFoundException("Integration not found for eventId=${event.eventId} and syncId=${event.syncId}")
        val companyIntegration = companyIntegrationRepository.findByAccountToken(integrationId)
            ?: throw jakarta.persistence.EntityNotFoundException("CompanyIntegration not found for eventId=${event.eventId} and integrationId=${integrationId}")
        val platformContractIntegration =
            platformContractIntegrationRepository.findFirstByPlatformEmployeeIdAndPlatformIdAndProviderIdAndIntegrationId(
                platformEmployeeId,
                companyIntegration.platform.id!!,
                companyIntegration.provider.id!!,
                companyIntegration.id!!
            )
        return Pair(companyIntegration, platformContractIntegration)
    }

    fun getCompanyIntegrationInfo(
        integrationId: Long,
        currentUserCompanyId: Long?,
        isOpsUser: Boolean = false,
    ): CompanyIntegrationInfo {
        log.info(
            "Received request to get Company Integration Info: integrationId: {}, currentUserCompanyId: {}",
            integrationId, currentUserCompanyId
        )

        val matchingIntegration = companyIntegrationRepository.findById(integrationId).get()
        validateIntegrationCompanyMatch(currentUserCompanyId, matchingIntegration.companyId, isOpsUser)

        var result = CompanyIntegrationInfo()
        result.companyId = matchingIntegration.companyId
        result.incomingSyncEnabled = matchingIntegration.incomingSyncEnabled
        result.outgoingSyncEnabled = matchingIntegration.outgoingSyncEnabled
        result.lastIncomingSyncStartTime = matchingIntegration.lastIncomingSyncTime
        result.incomingSyncInProgress = matchingIntegration.incomingSyncInProgress
        result.incomingImportInProgress = matchingIntegration.importInProgress
        result.timeOffSyncEnabled = matchingIntegration.timeOffSyncEnabled
        result.incomingContractorSyncEnabled = matchingIntegration.incomingContractorSyncEnabled
        result.incomingContractorSyncInProgress = matchingIntegration.incomingContractorSyncInProgress
        result.lastIncomingContractorSyncStartTime = matchingIntegration.lastIncomingContractorSyncTime

        var syncs = syncRepository.findByIntegrationId(matchingIntegration.accountToken)
        if (syncs.isEmpty()) {
            return result
        }
        val mostRecentSync = syncs.maxByOrNull { it.startTime } ?: return result
        // result.incomingSyncInProgress = mostRecentSync.inProgress

        if (matchingIntegration.lastIncomingSyncTime != null) {
            result.lastIncomingSyncId = mostRecentSync.syncId
        }
        return result
    }

    fun getCompanyIntegrationByExternalCompanyIdAndPlatformName(
        externalCompanyId: String?,
        platformName: String?,
    ): JpaCompanyIntegration {
        if (externalCompanyId.isNullOrBlank() || platformName.isNullOrBlank()) {
            throw IntegrationIllegalArgumentException("Neither externalCompanyId nor platformName can be null or blank")
        }
        val provider = providerRepository.findFirstByName(ProviderName.TRINET)
            ?: throw IntegrationIllegalStateException("Provider with name TRINET not found")
        val platform = platformRepository.findFirstByCategoryAndNameWithCaseInsensitive(PlatformCategory.HRIS, platformName)
            ?: throw IntegrationIllegalStateException("Platform data for $platformName not found")

        return companyIntegrationRepository.findByProviderIdAndPlatformIdAndExternalCompanyId(
            providerId = provider.id!!,
            platformId = platform.id!!,
            externalCompanyId = externalCompanyId
        )?.firstOrNull() ?: throw IntegrationIllegalStateException("Not found active company integration with externalCompanyId $externalCompanyId, platformName $platformName")
    }

    fun getSyncSummaryResultDownloadableFile(syncId: String, companyId: Long?): SyncSummaryResultDownloadOutput {
        val manualSync = manualSyncRepository.findBySyncId(syncId).orElse(null)
            ?: throw IntegrationIllegalArgumentException("Manual sync not found for syncId=$syncId")

        if (manualSync.status == SyncStatus.IN_PROGRESS) {
            return SyncSummaryResultDownloadOutput
                .newBuilder()
                .isDownloaded(false)
                .reportFile(null)
                .build()
        }

        val manualSyncs = manualSyncRepository.findByIntegrationIdOrderByCreatedOnDesc(manualSync.integrationId)
        if (manualSyncs.isNullOrEmpty() || (manualSync.syncId != manualSyncs[0].syncId)) {
            log.error("Failed for not latest manual sync")
            return SyncSummaryResultDownloadOutput
                .newBuilder()
                .isDownloaded(false)
                .reportFile(null)
                .build()
        }

        val latestManualSyncStartTime = manualSync.startedOn
        val events = eventLogRepository.findBySyncId(manualSyncs[0].syncId)
        val allEvents = mutableListOf<JpaEventLog>()
        allEvents.addAll(events)
        if (manualSyncs.size >= 2) {
            val prevManualSyncStartTime = manualSyncs[1].startedOn
            val summaryEvents = eventLogRepository.findByCompanyIdAndEventTimeRange(
                companyId,
                prevManualSyncStartTime,
                latestManualSyncStartTime,
                listOf(
                    EventType.INCOMING_MEMBER_BASIC_DETAILS_UPDATED,
                    EventType.INCOMING_MEMBER_ADDRESS_UPDATED,
                    EventType.INCOMING_MEMBER_LEGAL_DATA_UPDATED,
                    EventType.INCOMING_OFFBOARDING_STATUS_UPDATE,
                    EventType.INCOMING_SALARY_REVIEW_ACTIVATED,
                    EventType.INCOMING_CONTRACT_DOCUMENT_STATUS_UPDATE,
                    EventType.INCOMING_PAYROLL_PAYSLIP_UPLOADED,
                    EventType.INCOMING_CONTRACT_WORK_EMAIL_CHANGED,
                )
            )
            if (summaryEvents != null) {
                allEvents.addAll(summaryEvents)
            }
        }

        log.info("Found ${allEvents.size} events for syncId: $syncId")

        val integration = companyIntegrationRepository.findById(manualSync.integrationId)
            .orElseThrow { IntegrationIllegalStateException("Integration not found for integrationId=${manualSync.integrationId}") }

        val resultFileByteArray = generateTriNetSyncSummaryResult(allEvents, integration) ?: throw IntegrationIllegalStateException("Empty sync result for sync id $syncId")
        val fileData = Base64.getEncoder().encodeToString(resultFileByteArray)
        val reportFile = DocumentReadable.newBuilder()
            .blob(fileData)
            .contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
            .id(System.currentTimeMillis())
            .name("SyncSummaryResult_" + System.currentTimeMillis())
            .extension("xlsx")
            .build()

        return SyncSummaryResultDownloadOutput
            .newBuilder()
            .isDownloaded(true)
            .reportFile(reportFile)
            .build()
    }

    @Transactional
    fun clearGPSyncFailedEvents(companyId: Long, platformId: Long): TaskResponse {
        log.info("Received request to clear GP sync failed events for companyId: $companyId and platformId: $platformId")

        val platform = platformRepository.findById(platformId)
        if (platform.isEmpty) {
            val message = "Platform not found for platformId=$platformId"
            log.error(message)
            return TaskResponse(false, message)
        }

        val integrations =
            companyIntegrationRepository.findEnabledIntegrationsByCompanyIdAndPlatformId(companyId, platformId)
        if (integrations.isNullOrEmpty()) {
            val message = "Integration not found for companyId=$companyId and platformId=$platformId"
            log.error(message)
            return TaskResponse(false, message)
        }

        val integration = integrations.first()

        return try {

            val platformEmployeeIds: List<String> =
                platformEmployeeDataRepository.findPlatformEmployeeIdsByIntegrationId(integration.id!!)
            log.info("Found ${platformEmployeeIds.size} platform employee IDs for integrationId=${integration.id}")

            platformEmployeeDataRepository.deleteByIntegrationId(integration.id!!)
            log.info("Deleted platform employee data for integrationId=${integration.id}")

            receivedEventRepository.deleteByIntegrationId(integration.accountToken)
            log.info("Deleted received events for integrationId=${integration.accountToken}")

            platformContractIntegrationRepository.deleteByPlatformEmployeeIdsAndPlatformId(
                platformEmployeeIds,
                platformId
            )
            log.info("Deleted platform contract integrations for platformEmployeeIds and platformId=$platformId")

            // Delete all records from pending_employee using integration id
            pendingEmployeeRepository.deleteByIntegrationId(integration.accountToken)
            log.info("Deleted all pending employees for integrationId=${integration.accountToken}")

            TaskResponse(true, "GP sync failed events cleared successfully")
        } catch (e: Exception) {
            val message = "Failed to clear GP sync failed events: ${e.message}"
            log.error(message, e)
            TaskResponse(false, message)
        }
    }

    fun triggerIntegrationSync(syncId: String?, eventId: String?, syncType: SyncType, isOpsUser: Boolean): TaskResponse {
        if (!isOpsUser) {
            throw BadRequestException("You are not allowed to trigger sync.")
        }
        if (syncType == SyncType.INCOMING) {
            handleGPEventsUpdate(syncId, eventId)
        }
        else if (syncType == SyncType.OUTGOING || syncType == SyncType.MANUAL_OUTGOING) {
            handleEOREventUpdate(eventId)
        }
        else if (syncType == SyncType.INCOMING_CONTRACTOR) {
            handleContractorEventUpdate(syncId,eventId)
        }
        return TaskResponse(true, "Successfully re-triggered sync")
    }
    private fun handleContractorEventUpdate(syncId: String?, eventId: String?) {
        if (syncId != null) {
            val events = receivedEventRepository.findBySyncIdAndAndProcessed(syncId, true)
            val updatedEvents = events.map {
                val updatedEvent = it.apply {
                    processed = false
                    errors = null
                }
                updatedEvent
            }
            receivedEventRepository.saveAll(updatedEvents)
        } else if (eventId != null) {
            val receivedEvent = receivedEventRepository.findByEventIdAndAndProcessed(eventId, true)
                .orElseThrow { EntityNotFoundException("Not found received event.") }
            val updatedReceivedEvent = receivedEvent.apply {
                processed = false
                errors = null
            }
            receivedEventRepository.save(updatedReceivedEvent)
        } else {
            throw IllegalArgumentException("Either syncId or eventId must be provided")
        }
    }

    private fun handleEOREventUpdate(eventId: String?) {
        if (eventId.isNullOrEmpty()) {
            throw BadRequestException("EventID should be provided for this sync type.")
        }
        val eventLog = eventLogService.findEventLogByEventId(eventId)
        val updatedEvent = eventLog.apply {
            status = EventStatus.TO_BE_PROCESSED
            retriesDone = 0
            retriesLeft = 3
            nextAttempt = LocalDateTime.now()
            errorMessage = null
        }
        eventLogRepository.save(updatedEvent)
    }

    @Transactional
    fun handleGPEventsUpdate(syncId: String?, eventId: String?) {
        if (syncId != null) {
            val events = receivedEventRepository.findBySyncIdAndAndProcessed(syncId, true)
            val updatedEvents = events.map {
                val updatedEvent = it.apply {
                    processed = false
                    errors = null
                }
                updatedEvent
            }
            receivedEventRepository.saveAll(updatedEvents)
        } else if (eventId != null) {
            val receivedEvent = receivedEventRepository.findByEventIdAndAndProcessed(eventId, true)
                .orElseThrow { EntityNotFoundException("Not found received event.") }
            val updatedReceivedEvent = receivedEvent.apply {
                processed = false
                errors = null
            }
            receivedEventRepository.save(updatedReceivedEvent)
        } else {
            throw IllegalArgumentException("Either syncId or eventId must be provided")
        }
    }

    @Job("Get received event list")
    fun getReceivedEventList(request: GetReceivedEventListRequest): List<JpaReceivedEvent>? {
        val companyId = request.companyId
        val platformID = request.platformID
        val syncId = request.syncId
        val searchTerm = request.searchTerm
        val startOffset = request.startOffset
        val pageSize = request.pageSize

        val integration = companyIntegrationRepository.findEnabledIntegrationsByCompanyIdAndPlatformId(companyId, platformID)
            ?: throw IntegrationIllegalStateException("Integration not found for companyId=$companyId and platformId=$platformID")
        return if (searchTerm.isNullOrEmpty() || searchTerm == " ") {
            val receivedEvents = receivedEventRepository.findByIntegrationIdAndSyncIdOrderByCreatedOn(integration.first().accountToken, syncId, PageRequest.of(startOffset, pageSize))
            log.info("Found ${receivedEvents.size} received events for companyId=$companyId, platformId=$platformID and syncId=$syncId")
            receivedEvents
        } else {
            val receivedEvent = receivedEventRepository.findByIntegrationIdAndSyncIdAndSearchTermOrderByCreatedOn(integration.first().id.toString(), syncId, searchTerm)
            log.info("Found ${receivedEvent.size} received events for companyId=$companyId, platformId=$platformID and syncId=$syncId and searchTerm=$searchTerm")
            receivedEvent
        }
    }

    @Job("Retry received event")
    fun retryReceivedEvent(request: RetryReceivedEventRequest): TaskResponse {
        if (!request.eventId.isNullOrEmpty()) {
            val eventId = request.eventId
            log.info { "Received request to retry received event with eventId: $eventId" }
            val updatedEvent = receivedEventRepository.findByEventId(eventId)
            log.info { "Found received event with eventId: $eventId" }
            updatedEvent.processed = false
            receivedEventRepository.save(updatedEvent)
            log.info { "Successfully updated received event with eventId: $eventId to processed = false" }
        } else if (!request.syncId.isNullOrEmpty()) {
            val syncId = request.syncId
            log.info { "Received request to retry received event with syncId: $syncId" }
            val updatedEvents = receivedEventRepository.findBySyncId(syncId)
            log.info { "Found ${updatedEvents.size} received events with syncId: $syncId" }
            val updatedCount = receivedEventRepository.updateProcessedStatusByExternalId(syncId, false)
            log.info { "Successfully updated ${updatedCount} received events with syncId: $syncId to processed = false" }
        } else if (!request.eventIdListCommaSeparated.isNullOrEmpty()) {
            val eventIds = request.eventIdListCommaSeparated.split(",")
            log.info { "Received request to retry received events with eventIds: $eventIds" }
            eventIds.forEach { eventId ->
                val updatedEvent = receivedEventRepository.findByEventId(eventId)
                log.info { "Found received event with eventId: $eventId" }
                updatedEvent.processed = false
                receivedEventRepository.save(updatedEvent)
                log.info { "Successfully updated received event with eventId: $eventId to processed = false" }
            }
        } else {
            throw IllegalArgumentException("Either eventId or syncId or eventIds must be provided")
        }

        return TaskResponse(true, "Successfully retried received event")
    }

    @Async
    fun handleIncomingContractorSyncEnabled(integration: JpaCompanyIntegration, syncId: String) {
        log.info("Handling Incoming contractor sync enabled for integrationId: ${integration.id}")

        // Start the contractor sync process
        integration.incomingContractorSyncInProgress = true
        integration.lastIncomingContractorSyncTime = LocalDateTime.now()
        companyIntegrationRepository.save(integration)

        try {
            // Trigger contractor sync via external adapter
            val accountToken = integration.accountToken
            knitAdapter.startSync(accountToken, PlatformCategory.HRIS)
                ?: throw RuntimeException("Could not initiate incoming contractor sync")

            log.info("Successfully initiated incoming contractor sync for integrationId: ${integration.id}")
        } catch (e: Exception) {
            log.error("Error initiating incoming contractor sync for integrationId: ${integration.id}", e)
            // Reset sync progress on failure
            integration.incomingContractorSyncInProgress = false
            companyIntegrationRepository.save(integration)
            throw e
        }
    }
}

data class RetryReceivedEventRequest(
    val eventId: String?,
    val syncId: String?,
    val eventIdListCommaSeparated: String?,
)

data class GetReceivedEventListRequest(
    val companyId: Long,
    val platformID: Long,
    val syncId: String,
    val searchTerm: String,
    val startOffset: Int,
    val pageSize: Int,
)
data class ContractsAnalysis(
    val addedContracts: Int,
    val updatedContracts: Int,
    val failedContracts: Int,
    val allProcessed: Boolean,
)